<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\RoleMenuPermissionAssignRequest;
use App\Http\Resources\Admin\MenuResource;
use App\Http\Resources\Admin\RoleResource;
use App\Models\Menu;
use App\Models\Role;
use App\Services\RoleService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group 角色菜单权限管理
 *
 * 管理角色的菜单权限分配
 */
class RoleMenuPermissionController extends Controller
{
    public function __construct(
        private RoleService $roleService
    ) {}

    /**
     * 获取角色的菜单权限
     *
     * @urlParam role integer required 角色ID. Example: 1
     *
     * @response {
     *   "role": {
     *      "id": 1,
     *      "name": "管理员",
     *      "guard_name": "admin",
     *      "description": "系统管理员角色",
     *      "users_count": 1,
     *      "permissions": [
     *          {
     *              "id": 1,
     *              "name": "新增",
     *              "path": "/user",
     *              "method": "POST"
     *          }
     *      ],
     *      "created_at": "2021-01-01 00:00:00",
     *      "updated_at": "2021-01-01 00:00:00"
     *   },
     *   "permissions": [
     *     {
     *       "menu_id": 1,
     *       "menu_name": "User",
     *       "menu_title": "用户管理",
     *       "has_menu_access": true,
     *       "permissions": [
     *         {
     *           "id": 1,
     *           "title": "新增",
     *           "auth_mark": "add"
     *         }
     *       ]
     *     }
     *   ]
     * }
     */
    public function show(Role $role): JsonResponse
    {
        $permissions = $this->roleService->getRoleMenuPermissions($role);

        return response()->json([
            'role' => new RoleResource($role),
            'permissions' => $permissions
        ]);
    }

    /**
     * 获取所有菜单及权限（用于权限分配页面）
     *
     * @response {
     *   "menus": [
     *     {
     *       "id": 1,
     *       "parent_id": 0,
     *       "name": "User",
     *       "path": "/user",
     *       "component": "User",
     *       "title": "用户管理",
     *       "icon": "user",
     *       "label": "user",
     *       "sort": 1,
     *       "is_hide": false,
     *       "is_hide_tab": false,
     *       "link": "https://www.baidu.com",
     *       "is_iframe": false,
     *       "keep_alive": true,
     *       "is_first_level": false,
     *       "fixed_tab": false,
     *       "active_path": "/user",
     *       "is_full_page": false,
     *       "show_badge": false,
     *       "show_text_badge": "new",
     *       "status": true,
     *       "meta": {
     *         "title": "用户管理",
     *         "icon": "user",
     *         "keepAlive": true,
     *         "showBadge": false,
     *         "showTextBadge": "new",
     *         "isHide": false,
     *         "isHideTab": false,
     *         "link": "https://www.baidu.com",
     *         "isIframe": false,
     *         "authList": [
     *           {
     *             "title": "用户列表",
     *             "authMark": "user:list"
     *           }
     *         ],
     *         "isFirstLevel": false,
     *         "fixedTab": false,
     *         "activePath": "/user",
     *         "isFullPage": false
     *       },
     *       "permissions": [
     *         {
     *           "id": 1,
     *           "menu_id": 1,
     *           "title": "用户列表",
     *           "auth_mark": "user:list",
     *           "sort": 1
     *         }
     *       ]
     *     }
     *   ]
     * }
     */
    public function getMenusForAssignment(): JsonResponse
    {
        $menus = Menu::with('permissions')
            ->where('status', true)
            ->orderBy('parent_id', 'asc')
            ->orderBy('sort', 'desc')
            ->orderBy('id', 'desc')
            ->get();

        return response()->json([
            'menus' => MenuResource::collection($menus)
        ]);
    }

    /**
     * 为角色分配菜单权限
     *
     * @urlParam role integer required 角色ID. Example: 1
     *
     * @bodyParam permissions array required 权限数组. Example: [{"menu_id": 1, "menu_permission_id": null}, {"menu_id": 2, "menu_permission_id": 1}]
     * @bodyParam permissions.*.menu_id integer required 菜单ID. Example: 1
     * @bodyParam permissions.*.menu_permission_id integer nullable 菜单权限ID，为空表示只有菜单访问权限. Example: 1
     *
     * @response {
     *   "message": "权限分配成功",
     *   "role": {
     *     "id": 1,
     *     "name": "管理员",
     *     "description": "系统管理员角色",
     *     "role_menu_permissions": [
     *       {
     *         "id": 1,
     *         "role_id": 1,
     *         "menu_id": 1,
     *         "menu_permission_id": null
     *       },
     *       {
     *         "id": 2,
     *         "role_id": 1,
     *         "menu_id": 2,
     *         "menu_permission_id": 1
     *       }
     *     ]
     *   }
     * }
     */
    public function assign(RoleMenuPermissionAssignRequest $request, Role $role): JsonResponse
    {
        $this->roleService->assignMenuPermissions($role, $request->permissions);

        return response()->json([
            'message' => '权限分配成功',
            'role' => new RoleResource($role->load('roleMenuPermissions'))
        ]);
    }

    /**
     * 同步角色的菜单权限（覆盖式）
     *
     * @urlParam role integer required 角色ID. Example: 1
     *
     * @bodyParam permissions array required 权限数组（将完全替换角色当前的权限）. Example: [{"menu_id": 1, "menu_permission_id": null}]
     * @bodyParam permissions.*.menu_id integer required 菜单ID. Example: 1
     * @bodyParam permissions.*.menu_permission_id integer nullable 菜单权限ID，为空表示只有菜单访问权限. Example: 1
     *
     * @response {
     *   "message": "权限同步成功",
     *   "role": {
     *     "id": 1,
     *     "name": "管理员",
     *     "description": "系统管理员角色",
     *     "role_menu_permissions": [
     *       {
     *         "id": 1,
     *         "role_id": 1,
     *         "menu_id": 1,
     *         "menu_permission_id": null
     *       }
     *     ]
     *   }
     * }
     */
    public function sync(RoleMenuPermissionAssignRequest $request, Role $role): JsonResponse
    {
        $this->roleService->syncMenuPermissions($role, $request->permissions);

        return response()->json([
            'message' => '权限同步成功',
            'role' => new RoleResource($role->load('roleMenuPermissions'))
        ]);
    }

    /**
     * 移除角色的菜单权限
     *
     * @urlParam role integer required 角色ID. Example: 1
     *
     * @bodyParam permissions array required 要移除的权限数组. Example: [{"menu_id": 1, "menu_permission_id": 1}]
     * @bodyParam permissions.*.menu_id integer required 菜单ID. Example: 1
     * @bodyParam permissions.*.menu_permission_id integer nullable 菜单权限ID. Example: 1
     *
     * @response {
     *   "message": "权限移除成功",
     *   "role": {
     *     "id": 1,
     *     "name": "管理员",
     *     "description": "系统管理员角色",
     *     "role_menu_permissions": [
     *       {
     *         "id": 1,
     *         "role_id": 1,
     *         "menu_id": 1,
     *         "menu_permission_id": null
     *       }
     *     ]
     *   }
     * }
     */
    public function remove(RoleMenuPermissionAssignRequest $request, Role $role): JsonResponse
    {
        $this->roleService->removeMenuPermissions($role, $request->permissions);

        return response()->json([
            'message' => '权限移除成功',
            'role' => new RoleResource($role->load('roleMenuPermissions'))
        ]);
    }
}
