## Autogenerated by <PERSON>ri<PERSON>. DO NOT MODIFY.

name: 生命周期管理
description: |-

  管理设备生命周期记录
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/lifecycles
    metadata:
      groupName: 生命周期管理
      groupDescription: |-

        管理设备生命周期记录
      subgroup: ''
      subgroupDescription: ''
      title: 获取生命周期列表
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页数量.
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      asset_id:
        name: asset_id
        description: 资产ID筛选.
        required: false
        example: AST-2024-001
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      type:
        name: type
        description: 类型筛选.
        required: false
        example: installation
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      start_date:
        name: start_date
        description: 开始日期筛选.
        required: false
        example: '2024-01-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      end_date:
        name: end_date
        description: 结束日期筛选.
        required: false
        example: '2024-12-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      initiator_id:
        name: initiator_id
        description: 发起人ID筛选.
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      acceptance_entity_id:
        name: acceptance_entity_id
        description: 验收主体ID筛选.
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 20
      asset_id: AST-2024-001
      type: installation
      start_date: '2024-01-01'
      end_date: '2024-12-31'
      initiator_id: 1
      acceptance_entity_id: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[{"id":null,"asset_id":null,"type":null,"date":null,"initiator":null,"content":null,"acceptance_entity":null,"acceptance_personnel":null,"acceptance_time":null,"created_at":null,"updated_at":null},{"id":null,"asset_id":null,"type":null,"date":null,"initiator":null,"content":null,"acceptance_entity":null,"acceptance_personnel":null,"acceptance_time":null,"created_at":null,"updated_at":null}],"links":{"first":"\/?page=1","last":"\/?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"\/?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"\/","per_page":20,"to":2,"total":2}}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/lifecycles
    metadata:
      groupName: 生命周期管理
      groupDescription: |-

        管理设备生命周期记录
      subgroup: ''
      subgroupDescription: ''
      title: 创建生命周期
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      asset_id:
        name: asset_id
        description: 资产ID.
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      type:
        name: type
        description: 类型（字典值）.
        required: true
        example: installation
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date:
        name: date
        description: 日期.
        required: true
        example: '2024-01-15'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      initiator_id:
        name: initiator_id
        description: 发起人ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      content:
        name: content
        description: 内容.
        required: true
        example: 采购了一批新的服务器设备
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      assistants:
        name: assistants
        description: 协助人员ID数组.
        required: true
        example:
          - 2
          - 3
        type: 'integer[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      acceptance_entity_id:
        name: acceptance_entity_id
        description: 验收主体ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      acceptance_personnel_id:
        name: acceptance_personnel_id
        description: 验收人员ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      acceptance_time:
        name: acceptance_time
        description: 验收时间.
        required: true
        example: '2024-01-20 14:30:00'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      attachments:
        name: attachments
        description: 附件ID数组.
        required: false
        example:
          - 1
          - 2
          - 3
        type: 'integer[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      asset_id: 1
      type: installation
      date: '2024-01-15'
      initiator_id: 1
      content: 采购了一批新的服务器设备
      assistants:
        - 2
        - 3
      acceptance_entity_id: 1
      acceptance_personnel_id: 1
      acceptance_time: '2024-01-20 14:30:00'
      attachments:
        - 1
        - 2
        - 3
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/lifecycles/{id}'
    metadata:
      groupName: 生命周期管理
      groupDescription: |-

        管理设备生命周期记录
      subgroup: ''
      subgroupDescription: ''
      title: 获取生命周期详情
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 生命周期ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/lifecycles/{id}'
    metadata:
      groupName: 生命周期管理
      groupDescription: |-

        管理设备生命周期记录
      subgroup: ''
      subgroupDescription: ''
      title: 更新生命周期
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 生命周期ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      asset_id:
        name: asset_id
        description: 资产ID.
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      type:
        name: type
        description: 类型（字典值）.
        required: false
        example: installation
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date:
        name: date
        description: 日期.
        required: false
        example: '2024-01-15'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      initiator_id:
        name: initiator_id
        description: 发起人ID.
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      content:
        name: content
        description: 内容.
        required: false
        example: 采购了一批新的服务器设备
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      assistants:
        name: assistants
        description: 协助人员ID数组.
        required: false
        example:
          - 2
          - 3
        type: 'integer[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      acceptance_entity_id:
        name: acceptance_entity_id
        description: 验收主体ID.
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      acceptance_personnel_id:
        name: acceptance_personnel_id
        description: 验收人员ID.
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      acceptance_time:
        name: acceptance_time
        description: 验收时间.
        required: false
        example: '2024-01-20 14:30:00'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      attachments:
        name: attachments
        description: 附件ID数组.
        required: false
        example:
          - 1
          - 2
          - 3
        type: 'integer[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      asset_id: 1
      type: installation
      date: '2024-01-15'
      initiator_id: 1
      content: 采购了一批新的服务器设备
      assistants:
        - 2
        - 3
      acceptance_entity_id: 1
      acceptance_personnel_id: 1
      acceptance_time: '2024-01-20 14:30:00'
      attachments:
        - 1
        - 2
        - 3
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/lifecycles/{id}'
    metadata:
      groupName: 生命周期管理
      groupDescription: |-

        管理设备生命周期记录
      subgroup: ''
      subgroupDescription: ''
      title: 删除生命周期
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 生命周期ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/lifecycles/entities/{entityId}/acceptance-personnel'
    metadata:
      groupName: 生命周期管理
      groupDescription: |-

        管理设备生命周期记录
      subgroup: ''
      subgroupDescription: ''
      title: 获取验收人员列表
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      entityId:
        name: entityId
        description: 验收主体ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      entityId: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/lifecycles/{lifecycleId}/follow-ups'
    metadata:
      groupName: 生命周期管理
      groupDescription: |-

        管理生命周期跟进记录
      subgroup: ''
      subgroupDescription: ''
      title: 获取生命周期的跟进记录列表
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      lifecycleId:
        name: lifecycleId
        description: 生命周期ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      lifecycleId: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/admin/lifecycles/{lifecycleId}/follow-ups'
    metadata:
      groupName: 生命周期管理
      groupDescription: |-

        管理生命周期跟进记录
      subgroup: ''
      subgroupDescription: ''
      title: 创建跟进记录
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      lifecycleId:
        name: lifecycleId
        description: 生命周期ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      lifecycleId: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      date:
        name: date
        description: 日期.
        required: true
        example: '2024-01-16'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      person_id:
        name: person_id
        description: 跟进人ID（必须是协助人员之一）.
        required: true
        example: 2
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      content:
        name: content
        description: 内容.
        required: true
        example: 已联系供应商确认发货时间
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      attachments:
        name: attachments
        description: 附件ID数组.
        required: false
        example:
          - 1
          - 2
          - 3
        type: 'integer[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      date: '2024-01-16'
      person_id: 2
      content: 已联系供应商确认发货时间
      attachments:
        - 1
        - 2
        - 3
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/lifecycles/{lifecycleId}/follow-ups/{id}'
    metadata:
      groupName: 生命周期管理
      groupDescription: |-

        管理生命周期跟进记录
      subgroup: ''
      subgroupDescription: ''
      title: 获取跟进记录详情
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      lifecycleId:
        name: lifecycleId
        description: 生命周期ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      id:
        name: id
        description: 跟进记录ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      lifecycleId: 1
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/lifecycles/{lifecycleId}/follow-ups/{id}'
    metadata:
      groupName: 生命周期管理
      groupDescription: |-

        管理生命周期跟进记录
      subgroup: ''
      subgroupDescription: ''
      title: 更新跟进记录
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      lifecycleId:
        name: lifecycleId
        description: 生命周期ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      id:
        name: id
        description: 跟进记录ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      lifecycleId: 1
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      date:
        name: date
        description: 日期.
        required: false
        example: '2024-01-16'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      person_id:
        name: person_id
        description: 跟进人ID（必须是协助人员之一）.
        required: false
        example: 2
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      content:
        name: content
        description: 内容.
        required: false
        example: 已联系供应商确认发货时间
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      attachments:
        name: attachments
        description: 附件ID数组.
        required: false
        example:
          - 1
          - 2
          - 3
        type: 'integer[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      date: '2024-01-16'
      person_id: 2
      content: 已联系供应商确认发货时间
      attachments:
        - 1
        - 2
        - 3
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/lifecycles/{lifecycleId}/follow-ups/{id}'
    metadata:
      groupName: 生命周期管理
      groupDescription: |-

        管理生命周期跟进记录
      subgroup: ''
      subgroupDescription: ''
      title: 删除跟进记录
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      lifecycleId:
        name: lifecycleId
        description: 生命周期ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      id:
        name: id
        description: 跟进记录ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      lifecycleId: 1
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
