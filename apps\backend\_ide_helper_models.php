<?php

// @formatter:off
// phpcs:ignoreFile
/**
 * A helper file for your Eloquent Models
 * Copy the phpDocs from this file to the correct Model,
 * And remove them from this file, to prevent double declarations.
 *
 * <AUTHOR> vd. <PERSON> <<EMAIL>>
 */


namespace App\Models{
/**
 * @property int $id
 * @property string $name 资产名称
 * @property string|null $brand 资产品牌
 * @property string|null $model 规格型号
 * @property string|null $serial_number 序列号
 * @property int|null $asset_category_id 资产分类ID
 * @property int|null $department_category_id 科室分类ID
 * @property int|null $industry_category_id 行业分类ID
 * @property string|null $asset_source 资产来源
 * @property string|null $asset_status 资产状态
 * @property string|null $asset_condition 成色
 * @property int|null $parent_id 主设备ID
 * @property string|null $region_code 区县代码
 * @property string|null $detailed_address 详细地址
 * @property \Illuminate\Support\Carbon|null $start_date 启用日期
 * @property int|null $warranty_period 合同质保期(月)
 * @property int|null $warranty_alert 质保期预警(天)
 * @property int|null $maintenance_cycle 维护周期(天)
 * @property int|null $expected_years 预计使用年限(年)
 * @property array<array-key, mixed>|null $related_entities 相关主体信息
 * @property string|null $remark 备注
 * @property int|null $created_by 创建人
 * @property int|null $updated_by 更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Category|null $assetCategory
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Asset> $children
 * @property-read int $children_count
 * @property-read \App\Models\User|null $creator
 * @property-read \App\Models\Category|null $departmentCategory
 * @property-read string $full_address
 * @property-read array $region_path
 * @property-read \App\Models\Category|null $industryCategory
 * @property-read Asset|null $parent
 * @property-read \App\Models\Region|null $region
 * @property-read \App\Models\User|null $updater
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset accessory()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset main()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereAssetCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereAssetCondition($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereAssetSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereAssetStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereBrand($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereDepartmentCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereDetailedAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereExpectedYears($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereIndustryCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereMaintenanceCycle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereModel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereRegionCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereRelatedEntities($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereSerialNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereWarrantyAlert($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereWarrantyPeriod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset withoutTrashed()
 */
	class Asset extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property string $file_name 原始文件名
 * @property string $file_path 存储路径
 * @property int $file_size 文件大小(字节)
 * @property string $mime_type MIME类型
 * @property string $storage_type 存储类型:local/alioss/qiniu/aws
 * @property string|null $md5_hash MD5哈希值
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AttachmentRelation> $attachables
 * @property-read int|null $attachables_count
 * @property-read \App\Models\User|null $creator
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Entity> $entities
 * @property-read int|null $entities_count
 * @property-read string $file_url
 * @property-read string $formatted_file_size
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereFileSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereMd5Hash($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereMimeType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereStorageType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment withoutTrashed()
 */
	class Attachment extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $attachment_id 附件ID
 * @property int $attachable_id 业务表主键
 * @property string $attachable_type 业务表类型
 * @property string|null $category 附件分类
 * @property int $sort 排序
 * @property string|null $description 附件描述
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $attachable
 * @property-read \App\Models\Attachment $attachment
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereAttachableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereAttachableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereAttachmentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereUpdatedAt($value)
 */
	class AttachmentRelation extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property string $name 分类名称
 * @property string $code 分类编码
 * @property int $parent_id 父级ID，0表示顶级
 * @property int $level 层级深度
 * @property int $sort 排序
 * @property int $status 状态：0-禁用，1-启用
 * @property string|null $remark 备注
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Category> $children
 * @property-read int $children_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Category> $descendants
 * @property-read int|null $descendants_count
 * @property-read Category|null $parent
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereLevel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Category whereUpdatedAt($value)
 */
	class Category extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property string $code 分类编码
 * @property string $name 分类名称
 * @property string|null $description 分类描述
 * @property int $sort 排序
 * @property bool $is_enabled 是否启用
 * @property int|null $created_by 创建人ID
 * @property int|null $updated_by 更新人ID
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\DictionaryItem> $enabledItems
 * @property-read int|null $enabled_items_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\DictionaryItem> $items
 * @property-read int|null $items_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory enabled()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereIsEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryCategory withoutTrashed()
 */
	class DictionaryCategory extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $category_id 分类ID
 * @property string $code 字典编码
 * @property string $value 字典值
 * @property string|null $label 显示标签
 * @property int $sort 排序
 * @property string|null $color 颜色值
 * @property string|null $icon 图标
 * @property array<array-key, mixed>|null $config 扩展配置
 * @property string|null $remark 备注
 * @property bool $is_enabled 是否启用
 * @property int|null $created_by 创建人ID
 * @property int|null $updated_by 更新人ID
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\DictionaryCategory $category
 * @property-read string $display_text
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem enabled()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem ofCategory($categoryId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem ofCategoryCode($categoryCode)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereConfig($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereIsEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem withoutTrashed()
 */
	class DictionaryItem extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property string $name 主体名称
 * @property string|null $tax_number 税号
 * @property string $entity_type 主体类型
 * @property string|null $address 地址
 * @property string|null $phone 联系电话
 * @property string|null $keywords 特征词
 * @property string|null $remark 备注
 * @property int|null $created_by 创建人
 * @property int|null $updated_by 更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\EntityContact> $contacts
 * @property-read int|null $contacts_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereEntityType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereKeywords($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereTaxNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity withoutTrashed()
 */
	class Entity extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $entity_id 主体ID
 * @property string $name 联系人姓名
 * @property string $phone 联系电话
 * @property string|null $position 职位
 * @property string|null $department 部门
 * @property int|null $created_by 创建人
 * @property int|null $updated_by 更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Entity $entity
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereEntityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact wherePosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityContact whereUpdatedBy($value)
 */
	class EntityContact extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int|null $asset_id 资产ID
 * @property string $type 类型
 * @property \Illuminate\Support\Carbon $date 日期
 * @property int|null $initiator_id 发起人ID
 * @property string|null $content 内容
 * @property int|null $acceptance_entity_id 验收主体ID
 * @property int|null $acceptance_personnel_id 验收人员ID
 * @property \Illuminate\Support\Carbon|null $acceptance_time 验收时间
 * @property int|null $created_by 创建人
 * @property int|null $updated_by 更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Entity|null $acceptanceEntity
 * @property-read \App\Models\EntityContact|null $acceptancePersonnel
 * @property-read \App\Models\Asset|null $asset
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User> $assistants
 * @property-read int|null $assistants_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \App\Models\User|null $creator
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\LifecycleFollowUp> $followUps
 * @property-read int|null $follow_ups_count
 * @property-read \App\Models\User|null $initiator
 * @property-read \App\Models\User|null $updater
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereAcceptanceEntityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereAcceptancePersonnelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereAcceptanceTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereAssetId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereInitiatorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle withoutTrashed()
 */
	class Lifecycle extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $lifecycle_id 生命周期ID
 * @property \Illuminate\Support\Carbon $date 日期
 * @property int $person_id 跟进人ID
 * @property string $content 内容
 * @property int|null $created_by 创建人
 * @property int|null $updated_by 更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \App\Models\User|null $creator
 * @property-read \App\Models\Lifecycle $lifecycle
 * @property-read \App\Models\User $person
 * @property-read \App\Models\User|null $updater
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereLifecycleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp wherePersonId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LifecycleFollowUp whereUpdatedBy($value)
 */
	class LifecycleFollowUp extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int|null $parent_id 父级ID
 * @property string $name 路由名称（唯一）
 * @property string $path 路由路径
 * @property string|null $component 组件路径
 * @property string $title 菜单标题
 * @property string|null $icon 菜单图标
 * @property string|null $label 权限标识
 * @property int $sort 排序
 * @property bool $is_hide 是否隐藏
 * @property bool $is_hide_tab 是否在标签页隐藏
 * @property string|null $link 外部链接
 * @property bool $is_iframe 是否为iframe
 * @property bool $keep_alive 是否缓存
 * @property bool $is_first_level 是否为一级菜单
 * @property bool $fixed_tab 是否固定标签页
 * @property string|null $active_path 激活菜单路径
 * @property bool $is_full_page 是否全屏页面
 * @property bool $show_badge 是否显示徽章
 * @property string|null $show_text_badge 文本徽章内容
 * @property bool $status 状态：1启用 0禁用
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read array $meta
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\MenuPermission> $permissions
 * @property-read int|null $permissions_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereActivePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereComponent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereFixedTab($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereIsFirstLevel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereIsFullPage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereIsHide($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereIsHideTab($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereIsIframe($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereKeepAlive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu wherePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereShowBadge($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereShowTextBadge($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereUpdatedAt($value)
 */
	class Menu extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $menu_id 菜单ID
 * @property string $title 权限名称
 * @property string $auth_mark 权限标识
 * @property int $sort 排序
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Menu|null $menu
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereAuthMark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereMenuId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MenuPermission whereUpdatedAt($value)
 */
	class MenuPermission extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $pid 父级ID
 * @property int $deep 层级深度：0省1市2区县
 * @property string $name 地区名称
 * @property string $pinyin_prefix 拼音首字母
 * @property string $pinyin 拼音全拼
 * @property string $ext_id 行政区划代码
 * @property string $ext_name 完整名称
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Region> $children
 * @property-read int|null $children_count
 * @property-read Region|null $parent
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region citiesOfProvince($provinceId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region districtsOfCity($cityId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region provinces()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region search($keyword)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region whereDeep($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region whereExtId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region whereExtName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region wherePid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region wherePinyin($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region wherePinyinPrefix($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Region whereUpdatedAt($value)
 */
	class Region extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property string $name 角色名称
 * @property string|null $description 角色描述
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\MenuPermission> $menuPermissions
 * @property-read int|null $menu_permissions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Menu> $menus
 * @property-read int|null $menus_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\RoleMenuPermission> $roleMenuPermissions
 * @property-read int|null $role_menu_permissions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User> $users
 * @property-read int|null $users_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Role newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Role newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Role query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Role whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Role whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Role whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Role whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Role whereUpdatedAt($value)
 */
	class Role extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $role_id 角色ID
 * @property int $menu_id 菜单ID
 * @property int|null $menu_permission_id 菜单权限ID，为空表示只有菜单访问权限
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Menu $menu
 * @property-read \App\Models\MenuPermission|null $menuPermission
 * @property-read \App\Models\Role $role
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission whereMenuId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission whereMenuPermissionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission whereRoleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission whereUpdatedAt($value)
 */
	class RoleMenuPermission extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $tenant_id 租户ID
 * @property string $username 用户名
 * @property string $password 密码
 * @property string|null $email 邮箱
 * @property string|null $phone 手机号
 * @property string $status 状态
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUsername($value)
 */
	class User extends \Eloquent {}
}

