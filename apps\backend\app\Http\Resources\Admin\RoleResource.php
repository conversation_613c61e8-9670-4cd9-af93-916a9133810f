<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RoleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     *
     * @response {
     *   "id": 1,
     *   "name": "管理员",
     *   "guard_name": "admin",
     *   "description": "系统管理员角色",
     *   "users_count": 1,
     *   "permissions": [
     *     {
     *       "id": 1,
     *       "name": "新增",
     *       "path": "/user",
     *       "method": "POST"
     *     }
     *   ],
     *   "created_at": "2021-01-01 00:00:00",
     *   "updated_at": "2021-01-01 00:00:00"
     * }
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'guard_name' => $this->guard_name,
            'description' => $this->description,
            'users_count' => $this->when(isset($this->users_count), $this->users_count),
            'permissions' => $this->when($this->relationLoaded('permissions'), function () {
                return $this->permissions->map(function ($permission) {
                    return [
                        'id' => $permission->id,
                        'name' => $permission->name,
                        'path' => $permission->path,
                        'method' => $permission->method,
                    ];
                });
            }),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
