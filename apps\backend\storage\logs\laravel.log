[2025-07-22 16:36:03] local.ERROR: There are no commands defined in the "db:" namespace.

Did you mean this?
    db {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"db:\" namespace.

Did you mean this?
    db at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('db:')
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('db::seed')
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-22 16:36:08] local.ERROR: TTY mode is not supported on Windows platform. {"exception":"[object] (Symfony\\Component\\Process\\Exception\\RuntimeException(code: 0): TTY mode is not supported on Windows platform. at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\process\\Process.php:1044)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\DbCommand.php(53): Symfony\\Component\\Process\\Process->setTty(true)
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\DbCommand->handle()
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\DbCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#15 {main}
"} 
[2025-07-22 16:59:32] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'yes_or_no' for key 'dictionary_categories_code_unique' (Connection: mysql, SQL: insert into `dictionary_categories` (`name`, `code`, `description`, `sort`, `is_enabled`, `updated_at`, `created_at`) values (统一布尔字典, yes_or_no, 是/否选项, 1, 1, 2025-07-22 16:59:32, 2025-07-22 16:59:32)) {"exception":"[object] (Illuminate\\Database\\UniqueConstraintViolationException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'yes_or_no' for key 'dictionary_categories_code_unique' (Connection: mysql, SQL: insert into `dictionary_categories` (`name`, `code`, `description`, `sort`, `is_enabled`, `updated_at`, `created_at`) values (统一布尔字典, yes_or_no, 是/否选项, 1, 1, 2025-07-22 16:59:32, 2025-07-22 16:59:32)) at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:817)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `di...', Array, Object(Closure))
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `di...', Array, Object(Closure))
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `di...', Array, 'id')
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3785): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `di...', Array, 'id')
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2204): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): Illuminate\\Database\\Eloquent\\Model->save()
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\DictionaryCategory))
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1187): tap(Object(App\\Models\\DictionaryCategory), Object(Closure))
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\database\\seeders\\DictionarySeeder.php(152): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DictionarySeeder->run()
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#48 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'yes_or_no' for key 'dictionary_categories_code_unique' at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `di...', Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `di...', Array, Object(Closure))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `di...', Array, Object(Closure))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `di...', Array, 'id')
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3785): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `di...', Array, 'id')
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2204): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): Illuminate\\Database\\Eloquent\\Model->save()
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\DictionaryCategory))
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1187): tap(Object(App\\Models\\DictionaryCategory), Object(Closure))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\database\\seeders\\DictionarySeeder.php(152): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DictionarySeeder->run()
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#50 {main}
"} 
[2025-07-22 17:20:42] local.ERROR: Class "App\Http\Resources\AssetResource" not found {"exception":"[object] (Error(code: 0): Class \"App\\Http\\Resources\\AssetResource\" not found at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Shared\\ApiResourceResponseTools.php:64)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Shared\\ApiResourceResponseTools.php(29): Knuckles\\Scribe\\Extracting\\Shared\\ApiResourceResponseTools::getApiResourceOrCollectionInstance('App\\\\Http\\\\Resour...', true, Object(Closure), Array, Array)
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\UseApiResourceTags.php(58): Knuckles\\Scribe\\Extracting\\Shared\\ApiResourceResponseTools::fetch('App\\\\Http\\\\Resour...', true, Object(Closure), Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\UseApiResourceTags.php(36): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags->getApiResourceResponseFromTags(Object(Mpociot\\Reflection\\DocBlock\\Tag), Array, Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(240): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags->__invoke(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(165): Knuckles\\Scribe\\Extracting\\Extractor->iterateThroughStrategies('responses', Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Object(Closure))
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(97): Knuckles\\Scribe\\Extracting\\Extractor->fetchResponses(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(125): Knuckles\\Scribe\\Extracting\\Extractor->processRoute(Object(Illuminate\\Routing\\Route), Array)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(72): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoFromLaravelApp(Array, Array, Array)
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(50): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoAndWriteToDisk(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), true)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Commands\\GenerateDocumentation.php(55): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->get()
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Knuckles\\Scribe\\Commands\\GenerateDocumentation->handle(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), Object(Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFactory))
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Knuckles\\Scribe\\Commands\\GenerateDocumentation), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#24 {main}
"} 
[2025-07-23 09:18:41] local.ERROR: Class "App\Http\Resources\AssetResource" not found {"exception":"[object] (Error(code: 0): Class \"App\\Http\\Resources\\AssetResource\" not found at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Shared\\ApiResourceResponseTools.php:64)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Shared\\ApiResourceResponseTools.php(29): Knuckles\\Scribe\\Extracting\\Shared\\ApiResourceResponseTools::getApiResourceOrCollectionInstance('App\\\\Http\\\\Resour...', true, Object(Closure), Array, Array)
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\UseApiResourceTags.php(58): Knuckles\\Scribe\\Extracting\\Shared\\ApiResourceResponseTools::fetch('App\\\\Http\\\\Resour...', true, Object(Closure), Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\UseApiResourceTags.php(36): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags->getApiResourceResponseFromTags(Object(Mpociot\\Reflection\\DocBlock\\Tag), Array, Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(240): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags->__invoke(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(165): Knuckles\\Scribe\\Extracting\\Extractor->iterateThroughStrategies('responses', Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Object(Closure))
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(97): Knuckles\\Scribe\\Extracting\\Extractor->fetchResponses(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(125): Knuckles\\Scribe\\Extracting\\Extractor->processRoute(Object(Illuminate\\Routing\\Route), Array)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(72): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoFromLaravelApp(Array, Array, Array)
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(50): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoAndWriteToDisk(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), true)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Commands\\GenerateDocumentation.php(55): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->get()
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Knuckles\\Scribe\\Commands\\GenerateDocumentation->handle(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), Object(Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFactory))
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Knuckles\\Scribe\\Commands\\GenerateDocumentation), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#24 {main}
"} 
[2025-07-23 09:19:24] local.ERROR: Class "App\Http\Resources\AssetResource" not found {"exception":"[object] (Error(code: 0): Class \"App\\Http\\Resources\\AssetResource\" not found at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Shared\\ApiResourceResponseTools.php:64)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Shared\\ApiResourceResponseTools.php(29): Knuckles\\Scribe\\Extracting\\Shared\\ApiResourceResponseTools::getApiResourceOrCollectionInstance('App\\\\Http\\\\Resour...', true, Object(Closure), Array, Array)
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\UseApiResourceTags.php(58): Knuckles\\Scribe\\Extracting\\Shared\\ApiResourceResponseTools::fetch('App\\\\Http\\\\Resour...', true, Object(Closure), Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\UseApiResourceTags.php(36): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags->getApiResourceResponseFromTags(Object(Mpociot\\Reflection\\DocBlock\\Tag), Array, Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(240): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags->__invoke(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(165): Knuckles\\Scribe\\Extracting\\Extractor->iterateThroughStrategies('responses', Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Object(Closure))
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(97): Knuckles\\Scribe\\Extracting\\Extractor->fetchResponses(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(125): Knuckles\\Scribe\\Extracting\\Extractor->processRoute(Object(Illuminate\\Routing\\Route), Array)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(72): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoFromLaravelApp(Array, Array, Array)
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(50): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoAndWriteToDisk(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), true)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Commands\\GenerateDocumentation.php(55): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->get()
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Knuckles\\Scribe\\Commands\\GenerateDocumentation->handle(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), Object(Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFactory))
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Knuckles\\Scribe\\Commands\\GenerateDocumentation), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#24 {main}
"} 
[2025-07-23 09:28:27] local.ERROR: Class "App\Http\Resources\AssetResource" not found {"exception":"[object] (Error(code: 0): Class \"App\\Http\\Resources\\AssetResource\" not found at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Shared\\ApiResourceResponseTools.php:64)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Shared\\ApiResourceResponseTools.php(29): Knuckles\\Scribe\\Extracting\\Shared\\ApiResourceResponseTools::getApiResourceOrCollectionInstance('App\\\\Http\\\\Resour...', true, Object(Closure), Array, Array)
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\UseApiResourceTags.php(58): Knuckles\\Scribe\\Extracting\\Shared\\ApiResourceResponseTools::fetch('App\\\\Http\\\\Resour...', true, Object(Closure), Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\UseApiResourceTags.php(36): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags->getApiResourceResponseFromTags(Object(Mpociot\\Reflection\\DocBlock\\Tag), Array, Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(240): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags->__invoke(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(165): Knuckles\\Scribe\\Extracting\\Extractor->iterateThroughStrategies('responses', Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Object(Closure))
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(97): Knuckles\\Scribe\\Extracting\\Extractor->fetchResponses(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(125): Knuckles\\Scribe\\Extracting\\Extractor->processRoute(Object(Illuminate\\Routing\\Route), Array)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(72): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoFromLaravelApp(Array, Array, Array)
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(50): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoAndWriteToDisk(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), true)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Commands\\GenerateDocumentation.php(55): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->get()
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Knuckles\\Scribe\\Commands\\GenerateDocumentation->handle(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), Object(Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFactory))
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Knuckles\\Scribe\\Commands\\GenerateDocumentation), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#24 {main}
"} 
[2025-07-23 09:29:30] local.ERROR: Class "App\Http\Resources\AssetResource" not found {"exception":"[object] (Error(code: 0): Class \"App\\Http\\Resources\\AssetResource\" not found at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Shared\\ApiResourceResponseTools.php:64)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Shared\\ApiResourceResponseTools.php(29): Knuckles\\Scribe\\Extracting\\Shared\\ApiResourceResponseTools::getApiResourceOrCollectionInstance('App\\\\Http\\\\Resour...', true, Object(Closure), Array, Array)
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\UseApiResourceTags.php(58): Knuckles\\Scribe\\Extracting\\Shared\\ApiResourceResponseTools::fetch('App\\\\Http\\\\Resour...', true, Object(Closure), Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\UseApiResourceTags.php(36): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags->getApiResourceResponseFromTags(Object(Mpociot\\Reflection\\DocBlock\\Tag), Array, Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(240): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags->__invoke(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(165): Knuckles\\Scribe\\Extracting\\Extractor->iterateThroughStrategies('responses', Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Object(Closure))
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(97): Knuckles\\Scribe\\Extracting\\Extractor->fetchResponses(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(125): Knuckles\\Scribe\\Extracting\\Extractor->processRoute(Object(Illuminate\\Routing\\Route), Array)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(72): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoFromLaravelApp(Array, Array, Array)
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(50): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoAndWriteToDisk(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), true)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Commands\\GenerateDocumentation.php(55): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->get()
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Knuckles\\Scribe\\Commands\\GenerateDocumentation->handle(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), Object(Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFactory))
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Knuckles\\Scribe\\Commands\\GenerateDocumentation), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#24 {main}
"} 
[2025-07-23 09:40:06] local.ERROR: Command "scribe:clear" is not defined.

Did you mean one of these?
    auth:clear-resets
    cache:clear
    config:clear
    event:clear
    optimize:clear
    queue:clear
    route:clear
    schedule:clear-cache
    scribe:config:diff
    scribe:generate
    scribe:strategy
    view:clear {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"scribe:clear\" is not defined.

Did you mean one of these?
    auth:clear-resets
    cache:clear
    config:clear
    event:clear
    optimize:clear
    queue:clear
    route:clear
    schedule:clear-cache
    scribe:config:diff
    scribe:generate
    scribe:strategy
    view:clear at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php:726)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('scribe:clear')
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
[2025-07-23 09:42:56] local.ERROR: Class "App\Http\Resources\AssetResource" not found {"exception":"[object] (Error(code: 0): Class \"App\\Http\\Resources\\AssetResource\" not found at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Shared\\ApiResourceResponseTools.php:64)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Shared\\ApiResourceResponseTools.php(29): Knuckles\\Scribe\\Extracting\\Shared\\ApiResourceResponseTools::getApiResourceOrCollectionInstance('App\\\\Http\\\\Resour...', true, Object(Closure), Array, Array)
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\UseApiResourceTags.php(58): Knuckles\\Scribe\\Extracting\\Shared\\ApiResourceResponseTools::fetch('App\\\\Http\\\\Resour...', true, Object(Closure), Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\UseApiResourceTags.php(36): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags->getApiResourceResponseFromTags(Object(Mpociot\\Reflection\\DocBlock\\Tag), Array, Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(240): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags->__invoke(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(165): Knuckles\\Scribe\\Extracting\\Extractor->iterateThroughStrategies('responses', Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Object(Closure))
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(97): Knuckles\\Scribe\\Extracting\\Extractor->fetchResponses(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(125): Knuckles\\Scribe\\Extracting\\Extractor->processRoute(Object(Illuminate\\Routing\\Route), Array)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(72): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoFromLaravelApp(Array, Array, Array)
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(50): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoAndWriteToDisk(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), true)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Commands\\GenerateDocumentation.php(55): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->get()
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Knuckles\\Scribe\\Commands\\GenerateDocumentation->handle(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), Object(Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFactory))
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Knuckles\\Scribe\\Commands\\GenerateDocumentation), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#24 {main}
"} 
[2025-07-23 09:43:30] local.ERROR: Class "App\Http\Resources\AssetResource" not found {"exception":"[object] (Error(code: 0): Class \"App\\Http\\Resources\\AssetResource\" not found at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Shared\\ApiResourceResponseTools.php:64)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Shared\\ApiResourceResponseTools.php(29): Knuckles\\Scribe\\Extracting\\Shared\\ApiResourceResponseTools::getApiResourceOrCollectionInstance('App\\\\Http\\\\Resour...', true, Object(Closure), Array, Array)
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\UseApiResourceTags.php(58): Knuckles\\Scribe\\Extracting\\Shared\\ApiResourceResponseTools::fetch('App\\\\Http\\\\Resour...', true, Object(Closure), Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\UseApiResourceTags.php(36): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags->getApiResourceResponseFromTags(Object(Mpociot\\Reflection\\DocBlock\\Tag), Array, Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(240): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags->__invoke(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(165): Knuckles\\Scribe\\Extracting\\Extractor->iterateThroughStrategies('responses', Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Object(Closure))
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(97): Knuckles\\Scribe\\Extracting\\Extractor->fetchResponses(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(125): Knuckles\\Scribe\\Extracting\\Extractor->processRoute(Object(Illuminate\\Routing\\Route), Array)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(72): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoFromLaravelApp(Array, Array, Array)
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(50): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoAndWriteToDisk(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), true)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\knuckleswtf\\scribe\\src\\Commands\\GenerateDocumentation.php(55): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->get()
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Knuckles\\Scribe\\Commands\\GenerateDocumentation->handle(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), Object(Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFactory))
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Knuckles\\Scribe\\Commands\\GenerateDocumentation), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#24 {main}
"} 
[2025-07-23 14:44:04] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select `id` fro...')
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `id` fro...', Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-07-23 14:44:04] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select `id` fro...')
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `id` fro...', Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-07-23 14:44:06] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select `id` fro...')
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `id` fro...', Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-07-23 14:44:06] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select `id` fro...')
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `id` fro...', Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-07-23 14:44:07] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select `id` fro...')
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `id` fro...', Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-07-23 14:44:07] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select `id` fro...')
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `id` fro...', Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-07-23 14:44:28] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select `id` fro...')
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `id` fro...', Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-07-23 14:44:28] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select `id` fro...')
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `id` fro...', Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-07-23 14:44:30] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select `id` fro...')
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `id` fro...', Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-07-23 14:44:30] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select `id` fro...')
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `id` fro...', Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-07-23 14:44:31] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select `id` fro...')
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `id` fro...', Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-07-23 14:44:32] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous (Connection: mysql, SQL: select `id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` = 1) at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'id' in field list is ambiguous at D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select `id` fro...')
#1 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `id` fro...', Array)
#2 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select `id` fro...', Array, Object(Closure))
#3 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select `id` fro...', Array, Object(Closure))
#4 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select `id` fro...', Array, true)
#5 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3378): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3374): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1051): Illuminate\\Database\\Query\\Builder->pluck('id', NULL)
#9 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('id')
#10 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#11 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'pluck', Array)
#12 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Models\\User.php(122): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#13 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Services\\MenuService.php(49): App\\Models\\User->getAccessibleMenus()
#14 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Controllers\\Admin\\MenuController.php(40): App\\Services\\MenuService->getMenuListByUser(Object(App\\Models\\User))
#15 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\MenuController->index(Object(Illuminate\\Http\\Request))
#16 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\MenuController), 'index')
#17 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#24 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\app\\Http\\Middleware\\ForceJsonResponse.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceJsonResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\work\\code\\php\\device-cloud-saas\\apps\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
