<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AssetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $assetId = $this->route('asset')?->id;

        return [
            // 基础信息
            'name' => 'required|string|max:200',
            'brand' => 'nullable|string|max:100',
            'model' => 'nullable|string|max:100',
            'serial_number' => [
                'nullable',
                'string',
                'max:100',
                Rule::unique('assets')->ignore($assetId),
            ],

            // 分类关联
            'asset_category_id' => 'nullable|integer|exists:categories,id',
            'department_category_id' => 'nullable|integer|exists:categories,id',
            'industry_category_id' => 'nullable|integer|exists:categories,id',

            // 字典字段 - 需要查询对应字典分类ID
            'asset_source' => 'nullable|string|exists:dictionary_items,code,category_id,2',
            'asset_status' => 'nullable|string|exists:dictionary_items,code,category_id,3',
            'asset_condition' => 'nullable|string|exists:dictionary_items,code,category_id,4',

            // 父子关系
            'parent_id' => 'nullable|integer|exists:assets,id',

            // 地址信息
            'region_code' => 'nullable|string|max:12|exists:regions,ext_id',
            'detailed_address' => 'nullable|string',

            // 时间和数值字段
            'start_date' => 'nullable|date',
            'warranty_period' => 'nullable|integer|min:0|max:999',
            'warranty_alert' => 'nullable|integer|min:0|max:999',
            'maintenance_cycle' => 'nullable|integer|min:0|max:9999',
            'expected_years' => 'nullable|integer|min:0|max:99',

            // JSON字段
            'related_entities' => 'nullable|array',
            'related_entities.*.entity_type' => 'required_with:related_entities|string',
            'related_entities.*.entity_id' => 'required_with:related_entities|integer|exists:entities,id',
            'related_entities.*.contact_name' => 'nullable|string|max:50',
            'related_entities.*.contact_phone' => 'nullable|string|max:20',
            'related_entities.*.position' => 'nullable|string|max:50',
            'related_entities.*.department' => 'nullable|string|max:50',

            // 其他字段
            'remark' => 'nullable|string',
            'attachments' => 'nullable|array',
            'attachments.*' => 'integer|exists:attachments,id',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => '资产名称',
            'brand' => '品牌',
            'model' => '规格型号',
            'serial_number' => '序列号',
            'asset_category_id' => '资产分类',
            'department_category_id' => '科室分类',
            'industry_category_id' => '行业分类',
            'asset_source' => '资产来源',
            'asset_status' => '资产状态',
            'asset_condition' => '成色',
            'parent_id' => '主设备',
            'region_code' => '地区代码',
            'detailed_address' => '详细地址',
            'start_date' => '启用日期',
            'warranty_period' => '合同质保期',
            'warranty_alert' => '质保期预警',
            'maintenance_cycle' => '维护周期',
            'expected_years' => '预计使用年限',
            'related_entities' => '相关主体',
            'related_entities.*.entity_type' => '主体类型',
            'related_entities.*.entity_id' => '主体',
            'related_entities.*.contact_name' => '联系人姓名',
            'related_entities.*.contact_phone' => '联系电话',
            'related_entities.*.position' => '职位',
            'related_entities.*.department' => '部门',
            'remark' => '备注',
            'attachments' => '附件',
            'attachments.*' => '附件ID',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'parent_id.exists' => '选择的主设备不存在',
            'related_entities.*.entity_id.exists' => '选择的主体不存在',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // 不能将自己设为主设备
            if ($this->input('parent_id') && $this->route('asset')?->id == $this->input('parent_id')) {
                $validator->errors()->add('parent_id', '资产不能关联自己作为主设备');
            }

            // 验证相关主体中同一类型不能重复
            $relatedEntities = $this->input('related_entities');
            if ($relatedEntities) {
                $entityTypes = array_column($relatedEntities, 'entity_type');
                if (count($entityTypes) !== count(array_unique($entityTypes))) {
                    $validator->errors()->add('related_entities', '相关主体中同一类型不能重复添加');
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // 前端已统一使用 snake_case，不需要转换
    }
}
