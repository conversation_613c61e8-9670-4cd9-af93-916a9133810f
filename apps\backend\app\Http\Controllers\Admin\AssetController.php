<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AssetRequest;
use App\Http\Resources\Admin\AssetResource;
use App\Models\Asset;
use App\Services\AssetService;
use Illuminate\Http\Request;

/**
 * @group 资产管理
 */
class AssetController extends Controller
{
    public function __construct(
        private AssetService $assetService
    ) {}

    /**
     * 获取资产列表
     *
     * @queryParam name string 资产名称搜索 Example: 办公电脑
     * @queryParam brand string 品牌搜索 Example: 联想
     * @queryParam serial_number string 序列号搜索 Example: ABC123456
     * @queryParam keyword string 通用搜索关键词（同时搜索名称、品牌、型号、序列号）Example: 联想
     * @queryParam asset_category_id int 资产分类ID Example: 1
     * @queryParam department_category_id int 科室分类ID Example: 2
     * @queryParam industry_category_id int 行业分类ID Example: 3
     * @queryParam asset_status string 资产状态（字典code） Example: in_use
     * @queryParam asset_condition string 成色（字典code） Example: brand_new
     * @queryParam asset_source string 资产来源（字典code） Example: purchase
     * @queryParam is_accessory boolean 是否附属设备 Example: false
     * @queryParam parent_id int 主设备ID Example: 1
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page int 每页条数 Example: 20
     *
     * @apiResourceCollection App\Http\Resources\Admin\AssetResource
     *
     * @apiResourceModel App\Models\Asset paginate=20
     */
    public function index(Request $request)
    {
        $assets = $this->assetService->paginate($request->all());

        return AssetResource::collection($assets);
    }

    /**
     * 获取可作为主设备的资产列表
     *
     * @queryParam exclude_id int 排除的资产ID（避免自己关联自己） Example: 5
     * @queryParam keyword string 搜索关键词 Example: 电脑
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page int 每页条数 Example: 20
     */
    public function mainAssets(Request $request)
    {
        $assets = $this->assetService->getMainAssets($request->all());

        return AssetResource::collection($assets);
    }

    /**
     * 创建资产
     *
     * @bodyParam name string required 资产名称 Example: 办公台式电脑
     * @bodyParam brand string 品牌 Example: 联想
     * @bodyParam model string 规格型号 Example: ThinkCentre M720
     * @bodyParam serial_number string 序列号 Example: ABC123456789
     * @bodyParam asset_category_id int 资产分类ID Example: 1
     * @bodyParam department_category_id int 科室分类ID Example: 2
     * @bodyParam industry_category_id int 行业分类ID Example: 3
     * @bodyParam asset_source string 资产来源（字典code） Example: purchase
     * @bodyParam asset_status string 资产状态（字典code） Example: new_unstocked
     * @bodyParam asset_condition string 成色（字典code） Example: brand_new
     * @bodyParam parent_id int 主设备ID（附属设备时必填） Example: 1
     * @bodyParam is_accessory boolean 是否附属设备 Example: false
     * @bodyParam province string 省份 Example: 广东省
     * @bodyParam city string 城市 Example: 广州市
     * @bodyParam district string 区县 Example: 增城区
     * @bodyParam detailed_address string 详细地址 Example: XX街道XX号XX大厦
     * @bodyParam start_date date 启用日期 Example: 2024-01-01
     * @bodyParam warranty_period int 合同质保期（月） Example: 36
     * @bodyParam warranty_alert int 质保期预警（天） Example: 30
     * @bodyParam maintenance_cycle int 维护周期（天） Example: 90
     * @bodyParam expected_years int 预计使用年限（年） Example: 5
     * @bodyParam related_entities array 相关主体信息 JSON数组
     * @bodyParam remark string 备注
     * @bodyParam attachments array 附件ID数组 Example: [1,2,3]
     */
    public function store(AssetRequest $request)
    {
        $asset = $this->assetService->create($request->validated());

        return (new AssetResource($asset))
            ->response()
            ->setStatusCode(201);
    }

    /**
     * 获取资产详情
     *
     * @urlParam asset int required 资产ID Example: 1
     */
    public function show(Asset $asset)
    {
        $asset->load([
            'assetCategory',
            'departmentCategory',
            'industryCategory',
            'parent',
            'children',
            'attachments',
        ]);

        return new AssetResource($asset);
    }

    /**
     * 更新资产
     *
     * @urlParam asset int required 资产ID Example: 1
     *
     * @bodyParam name string required 资产名称 Example: 办公台式电脑
     * @bodyParam brand string 品牌 Example: 联想
     * @bodyParam model string 规格型号 Example: ThinkCentre M720
     * @bodyParam serial_number string 序列号 Example: ABC123456789
     * @bodyParam asset_category_id int 资产分类ID Example: 1
     * @bodyParam department_category_id int 科室分类ID Example: 2
     * @bodyParam industry_category_id int 行业分类ID Example: 3
     * @bodyParam asset_source string 资产来源（字典code） Example: purchase
     * @bodyParam asset_status string 资产状态（字典code） Example: in_use
     * @bodyParam asset_condition string 成色（字典code） Example: brand_new
     * @bodyParam parent_id int 主设备ID（附属设备时必填） Example: 1
     * @bodyParam is_accessory boolean 是否附属设备 Example: false
     * @bodyParam province string 省份 Example: 广东省
     * @bodyParam city string 城市 Example: 广州市
     * @bodyParam district string 区县 Example: 增城区
     * @bodyParam detailed_address string 详细地址 Example: XX街道XX号XX大厦
     * @bodyParam start_date date 启用日期 Example: 2024-01-01
     * @bodyParam warranty_period int 合同质保期（月） Example: 36
     * @bodyParam warranty_alert int 质保期预警（天） Example: 30
     * @bodyParam maintenance_cycle int 维护周期（天） Example: 90
     * @bodyParam expected_years int 预计使用年限（年） Example: 5
     * @bodyParam related_entities array 相关主体信息 JSON数组
     * @bodyParam remark string 备注
     * @bodyParam attachments array 附件ID数组 Example: [1,2,3]
     */
    public function update(AssetRequest $request, Asset $asset)
    {
        $asset = $this->assetService->update($asset, $request->validated());

        return new AssetResource($asset);
    }

    /**
     * 删除资产
     *
     * @urlParam asset int required 资产ID Example: 1
     */
    public function destroy(Asset $asset)
    {
        $this->assetService->delete($asset);

        return response()->noContent();
    }
}
