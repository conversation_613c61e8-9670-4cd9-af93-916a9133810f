<?php

namespace App\Enums;

/**
 * 资产来源
 *
 * 此文件由 php artisan dictionary:generate-enums 命令自动生成
 * 请勿手动修改，如需更改请在字典管理中修改后重新生成
 *
 * @generated 2025-07-21 12:53:10
 */
enum AssetSource: string
{
    case PRODUCE = 'produce';
    case PURCHASE = 'purchase';
    case TRANSFER = 'transfer';
    case DONATE = 'donate';

    /**
     * 获取枚举对应的中文标签
     */
    public function label(): string
    {
        return match ($this) {
            self::PRODUCE => '自产',
            self::PURCHASE => '采购',
            self::TRANSFER => '转让',
            self::DONATE => '捐赠',
        };
    }

    /**
     * 根据值获取枚举实例
     */
    public static function tryFromValue(string $value): ?self
    {
        return self::tryFrom($value);
    }

    /**
     * 检查值是否有效
     */
    public static function isValid(string $value): bool
    {
        return self::tryFrom($value) !== null;
    }
}
