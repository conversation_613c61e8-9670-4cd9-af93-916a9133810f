<?php

namespace App\Models;

use App\Traits\HasAttachments;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Asset extends Model
{
    use HasAttachments, HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'brand',
        'model',
        'serial_number',
        'asset_category_id',
        'department_category_id',
        'industry_category_id',
        'asset_source',
        'asset_status',
        'asset_condition',
        'parent_id',
        'region_code',
        'detailed_address',
        'start_date',
        'warranty_period',
        'warranty_alert',
        'maintenance_cycle',
        'expected_years',
        'related_entities',
        'remark',
        'created_by',
        'updated_by',
    ];

    protected $hidden = ['deleted_at'];

    protected $casts = [
        'start_date' => 'date',
        'warranty_period' => 'integer',
        'warranty_alert' => 'integer',
        'maintenance_cycle' => 'integer',
        'expected_years' => 'integer',
        'related_entities' => 'array',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 获取资产分类
     */
    public function assetCategory(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'asset_category_id');
    }

    /**
     * 获取科室分类
     */
    public function departmentCategory(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'department_category_id');
    }

    /**
     * 获取行业分类
     */
    public function industryCategory(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'industry_category_id');
    }

    /**
     * 获取主设备
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Asset::class, 'parent_id');
    }

    /**
     * 获取附属设备
     */
    public function children(): HasMany
    {
        return $this->hasMany(Asset::class, 'parent_id');
    }

    /**
     * 获取创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取更新人
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * 获取地区信息
     */
    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class, 'region_code', 'ext_id');
    }

    /**
     * 获取完整地址
     */
    public function getFullAddressAttribute(): string
    {
        $region = $this->region;
        if (! $region) {
            return $this->detailed_address ?? '';
        }

        $path = $region->getFullPath();
        $regionNames = array_column($path, 'name');

        return implode('', $regionNames).($this->detailed_address ?? '');
    }

    /**
     * 获取地区路径
     */
    public function getRegionPathAttribute(): array
    {
        $region = $this->region;

        return $region ? $region->getFullPath() : [];
    }

    /**
     * 作用域：只查询启用的资产
     */
    public function scopeActive($query)
    {
        return $query->whereNotIn('asset_status', ['scrap_registered']);
    }

    /**
     * 作用域：只查询主设备（非附属设备）
     */
    public function scopeMain($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * 作用域：只查询附属设备
     */
    public function scopeAccessory($query)
    {
        return $query->whereNotNull('parent_id');
    }

    /**
     * 判断是否有附属设备
     */
    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }

    /**
     * 获取附属设备数量
     */
    public function getChildrenCountAttribute(): int
    {
        return $this->children()->count();
    }
}
