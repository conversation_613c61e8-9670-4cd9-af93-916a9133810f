name: 角色菜单权限管理
description: |-

  管理角色的菜单权限分配
endpoints:
  -
    httpMethods:
      - GET
    uri: 'api/admin/roles/{role_id}/menu-permissions'
    metadata:
      groupName: 角色菜单权限管理
      groupDescription: |-

        管理角色的菜单权限分配
      subgroup: ''
      subgroupDescription: ''
      title: 获取角色的菜单权限
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      role_id:
        name: role_id
        description: 'The ID of the role.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: 角色ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      role_id: 1
      role: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "role": {
               "id": 1,
               "name": "管理员",
               "guard_name": "admin",
               "description": "系统管理员角色",
               "users_count": 1,
               "permissions": [
                   {
                       "id": 1,
                       "name": "新增",
                       "path": "/user",
                       "method": "POST"
                   }
               ],
               "created_at": "2021-01-01 00:00:00",
               "updated_at": "2021-01-01 00:00:00"
            },
            "permissions": [
              {
                "menu_id": 1,
                "menu_name": "User",
                "menu_title": "用户管理",
                "has_menu_access": true,
                "permissions": [
                  {
                    "id": 1,
                    "title": "新增",
                    "auth_mark": "add"
                  }
                ]
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/admin/roles/{role_id}/menu-permissions/assign'
    metadata:
      groupName: 角色菜单权限管理
      groupDescription: |-

        管理角色的菜单权限分配
      subgroup: ''
      subgroupDescription: ''
      title: 为角色分配菜单权限
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      role_id:
        name: role_id
        description: 'The ID of the role.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: 角色ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      role_id: 1
      role: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      permissions:
        name: permissions
        description: 权限数组.
        required: true
        example:
          -
            menu_id: 1
            menu_permission_id: null
          -
            menu_id: 2
            menu_permission_id: 1
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions[].menu_id':
        name: 'permissions[].menu_id'
        description: 'The <code>id</code> of an existing record in the menus table.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions[].menu_permission_id':
        name: 'permissions[].menu_permission_id'
        description: 'The <code>id</code> of an existing record in the menu_permissions table.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'permissions.*.menu_id':
        name: 'permissions.*.menu_id'
        description: 菜单ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions.*.menu_permission_id':
        name: 'permissions.*.menu_permission_id'
        description: 'nullable 菜单权限ID，为空表示只有菜单访问权限.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      permissions:
        -
          menu_id: 1
          menu_permission_id: null
        -
          menu_id: 2
          menu_permission_id: 1
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "message": "权限分配成功",
            "role": {
              "id": 1,
              "name": "管理员",
              "description": "系统管理员角色",
              "role_menu_permissions": [
                {
                  "id": 1,
                  "role_id": 1,
                  "menu_id": 1,
                  "menu_permission_id": null
                },
                {
                  "id": 2,
                  "role_id": 1,
                  "menu_id": 2,
                  "menu_permission_id": 1
                }
              ]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/admin/roles/{role_id}/menu-permissions/sync'
    metadata:
      groupName: 角色菜单权限管理
      groupDescription: |-

        管理角色的菜单权限分配
      subgroup: ''
      subgroupDescription: ''
      title: 同步角色的菜单权限（覆盖式）
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      role_id:
        name: role_id
        description: 'The ID of the role.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: 角色ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      role_id: 1
      role: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      permissions:
        name: permissions
        description: 权限数组（将完全替换角色当前的权限）.
        required: true
        example:
          -
            menu_id: 1
            menu_permission_id: null
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions[].menu_id':
        name: 'permissions[].menu_id'
        description: 'The <code>id</code> of an existing record in the menus table.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions[].menu_permission_id':
        name: 'permissions[].menu_permission_id'
        description: 'The <code>id</code> of an existing record in the menu_permissions table.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'permissions.*.menu_id':
        name: 'permissions.*.menu_id'
        description: 菜单ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions.*.menu_permission_id':
        name: 'permissions.*.menu_permission_id'
        description: 'nullable 菜单权限ID，为空表示只有菜单访问权限.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      permissions:
        -
          menu_id: 1
          menu_permission_id: null
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "message": "权限同步成功",
            "role": {
              "id": 1,
              "name": "管理员",
              "description": "系统管理员角色",
              "role_menu_permissions": [
                {
                  "id": 1,
                  "role_id": 1,
                  "menu_id": 1,
                  "menu_permission_id": null
                }
              ]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/roles/{role_id}/menu-permissions/remove'
    metadata:
      groupName: 角色菜单权限管理
      groupDescription: |-

        管理角色的菜单权限分配
      subgroup: ''
      subgroupDescription: ''
      title: 移除角色的菜单权限
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      role_id:
        name: role_id
        description: 'The ID of the role.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: 角色ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      role_id: 1
      role: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      permissions:
        name: permissions
        description: 要移除的权限数组.
        required: true
        example:
          -
            menu_id: 1
            menu_permission_id: 1
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions[].menu_id':
        name: 'permissions[].menu_id'
        description: 'The <code>id</code> of an existing record in the menus table.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions[].menu_permission_id':
        name: 'permissions[].menu_permission_id'
        description: 'The <code>id</code> of an existing record in the menu_permissions table.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'permissions.*.menu_id':
        name: 'permissions.*.menu_id'
        description: 菜单ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions.*.menu_permission_id':
        name: 'permissions.*.menu_permission_id'
        description: 'nullable 菜单权限ID.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      permissions:
        -
          menu_id: 1
          menu_permission_id: 1
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "message": "权限移除成功",
            "role": {
              "id": 1,
              "name": "管理员",
              "description": "系统管理员角色",
              "role_menu_permissions": [
                {
                  "id": 1,
                  "role_id": 1,
                  "menu_id": 1,
                  "menu_permission_id": null
                }
              ]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/menus/for-assignment
    metadata:
      groupName: 角色菜单权限管理
      groupDescription: |-

        管理角色的菜单权限分配
      subgroup: ''
      subgroupDescription: ''
      title: 获取所有菜单及权限（用于权限分配页面）
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "menus": [
              {
                "id": 1,
                "parent_id": 0,
                "name": "User",
                "path": "/user",
                "component": "User",
                "title": "用户管理",
                "icon": "user",
                "label": "user",
                "sort": 1,
                "is_hide": false,
                "is_hide_tab": false,
                "link": "https://www.baidu.com",
                "is_iframe": false,
                "keep_alive": true,
                "is_first_level": false,
                "fixed_tab": false,
                "active_path": "/user",
                "is_full_page": false,
                "show_badge": false,
                "show_text_badge": "new",
                "status": true,
                "meta": {
                  "title": "用户管理",
                  "icon": "user",
                  "keepAlive": true,
                  "showBadge": false,
                  "showTextBadge": "new",
                  "isHide": false,
                  "isHideTab": false,
                  "link": "https://www.baidu.com",
                  "isIframe": false,
                  "authList": [
                    {
                      "title": "用户列表",
                      "authMark": "user:list"
                    }
                  ],
                  "isFirstLevel": false,
                  "fixedTab": false,
                  "activePath": "/user",
                  "isFullPage": false
                },
                "permissions": [
                  {
                    "id": 1,
                    "menu_id": 1,
                    "title": "用户列表",
                    "auth_mark": "user:list",
                    "sort": 1
                  }
                ]
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
